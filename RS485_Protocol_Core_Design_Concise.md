# RS485 Protocol Core Design - Concise Overview

## 1. Executive Summary

**Purpose**: Windows RS485 driver for AI-SLDAP device communication using ZES protocol
**Architecture**: Windows User-Mode Driver Framework (UMDF 2) with integrated FTDI VCP driver
**Deliverable**: Single executable application (.exe) with complete RS485 communication solution
**Target**: 32 devices maximum (30 slaves + 1 master) on RS485 bus topology

## 2. System Architecture - Three-Layer Design

### 2.1 Layer Structure

```
┌─────────────────────────────────────────────────────────┐
│                 User Application Layer                  │
│              (Business Logic & UI)                      │
└─────────────────────────────────────────────────────────┘
                            │
                    High-Level API Calls
                            │
┌─────────────────────────────────────────────────────────┐
│                RS485 Driver API Layer                   │
│        (5 API Categories + Management Functions)        │
│   • Error Handle API    • Master Broadcasting API      │
│   • Master Assign API   • Master Request API           │
│   • Slave Response API                                  │
└─────────────────────────────────────────────────────────┘
                            │
                   DeviceIoControl Interface
                            │
┌─────────────────────────────────────────────────────────┐
│            Windows UMDF 2 Driver Layer                  │
│    (Protocol Processing + FTDI VCP Integration)         │
│   • Frame Processing    • Buffer Management             │
│   • CRC Validation     • Address Filtering              │
│   • Function Code Routing                               │
└─────────────────────────────────────────────────────────┘
                            │
                      USB-RS485 Hardware
                            │
┌─────────────────────────────────────────────────────────┐
│                   Physical Layer                        │
│              RS485 Bus + FPGA Slaves                    │
└─────────────────────────────────────────────────────────┘
```

### 2.2 Data Flow Architecture

**Frame Structure (16 bytes total)**:
- Header(1) + ID(1) + **Payload(12)** + CRC(1) + Trailer(1)
- **User API handles only 12-byte payload**: Key(4) + Value(8)
- **Driver handles protocol overhead**: Header, ID, CRC, Trailer

**Buffer Management**:
- **Uplink Buffer**: 5 frames × 12 bytes payload
- **Downlink Buffer**: 10 frames × 12 bytes payload
- **FIFO Processing**: Strict First-In-First-Out ordering

## 3. API Categories and Function Code Mapping

| Function Code | Binary | API Category | Description | Used By |
|:-------------:|:------:|:-------------|:------------|:--------|
| **0b111** | 0x07 | Broadcasting + Assign Data | System config + User config | Master |
| **0b110** | 0x06 | Master Request | Data queries | Master |
| **0b010** | 0x02 | Slave Response | Assign acknowledgments | Slave |
| **0b001** | 0x01 | Slave Response | Data responses | Slave |
| **0b000** | 0x00 | Error Handle | Retry mechanism | Both |

## 4. Priority Implementation - User Configuration (U-series)

### 4.1 🔥 FIRST IMPLEMENTATION PRIORITY

| Command | Description | Value Range | API Call |
|---------|-------------|-------------|----------|
| **U001** | SEL detection threshold | 40-500 milliampere | `configureUserSettings('U001', 250)` |
| **U002** | SEL maximum amplitude threshold | 1000-2000 milliampere | `configureUserSettings('U002', 1500)` |
| **U003** | SEL detection count before power cycle | 1-5 | `configureUserSettings('U003', 3)` |
| **U004** | Power cycle duration | 200,400,600,800,1000 ms | `configureUserSettings('U004', 600)` |
| **U005** | GPIO input channel control | Channel + Enable flag | `configureUserSettings('U005', channelData)` |
| **U006** | GPIO output channel control | Channel + Enable flag | `configureUserSettings('U006', channelData)` |

### 4.2 GPIO Value Packing for U005/U006

```cpp
// GPIO Configuration Examples:
// Enable GPIO input channel 0:  configureUserSettings('U005', 0x100000000ULL)
// Disable GPIO input channel 1: configureUserSettings('U005', 0x000000001ULL)
// Enable GPIO output channel 1:  configureUserSettings('U006', 0x100000001ULL)

// Format: Lower 32 bits = Channel ID, Upper 32 bits = Enable Flag
uint64_t gpioValue = (static_cast<uint64_t>(enableFlag) << 32) | channelId;
```

## 5. Complete API Reference

### 5.1 Management APIs (FTDI-Style)

```cpp
// Port Management
ConnectionResult openPort(const std::string& portName);
ConnectionResult closePort();
bool isPortOpen() const;

// Buffer Management (Mandatory before data transmission)
BufferResult getBufferStatus(BufferStatus& status);
BufferResult clearBuffer(BufferType bufferType = BufferType::BOTH);

// Device Discovery
static EnumerationResult enumerateDevices(std::vector<DeviceInfo>& deviceList);
```

### 5.2 System Configuration (S-series)

```cpp
// System Broadcasting API - Only one slave connected
ConfigurationResult configureSystemSettings(const std::string& commandKey, uint64_t value);

// Examples:
driver.configureSystemSettings("S001", 5);        // Set slave address to 5
driver.configureSystemSettings("S002", 115200);   // Set baud rate to 115200
```

### 5.3 User Configuration (U-series) - PRIORITY

```cpp
// User Settings API - Uses address from S001
ConfigurationResult configureUserSettings(const std::string& commandKey, uint64_t value);

// Examples:
driver.configureUserSettings("U001", 250);        // SEL threshold: 250 mA
driver.configureUserSettings("U002", 1500);       // Max amplitude: 1500 mA
driver.configureUserSettings("U003", 3);          // Detection count: 3
driver.configureUserSettings("U004", 600);        // Power cycle: 600 ms
driver.configureUserSettings("U005", 0x100000000ULL); // Enable GPIO input ch0
```

### 5.4 Data Request (A-series)

```cpp
// Master Request API - Non-blocking design
RequestResult requestData(const std::string& dataKey);

// Examples:
driver.requestData("A001");  // Request SEL event log
driver.requestData("A002");  // Request device status
driver.requestData("A003");  // Request firmware version
driver.requestData("A004");  // Request system statistics
driver.requestData("A005");  // Request current configuration
```

### 5.5 Response Handling

```cpp
// Slave Response API - Handles both acknowledgments and data
ResponseResult receiveSlaveResponse(uint8_t slaveAddress, uint8_t responseData[12],
                                   uint32_t timeoutMs = 1000);

// Example usage:
uint8_t responseData[12];
ResponseResult result = driver.receiveSlaveResponse(5, responseData, 100);
if (result == ResponseResult::SUCCESS) {
    // Extract key and value from 12-byte payload
    std::string key = extractKey(responseData);      // Bytes 0-3
    uint32_t value = extractInteger(responseData);   // Bytes 4-7
}
```

## 6. Typical Usage Workflow

### 6.1 Device Setup Sequence

```cpp
// 1. Initialize driver and open port
RS485Driver driver;
driver.openPort("COM3");

// 2. Set slave address (only one slave connected)
driver.configureSystemSettings("S001", 5);

// 3. Configure user settings
driver.configureUserSettings("U001", 250);   // SEL threshold
driver.configureUserSettings("U002", 1500);  // Max amplitude
driver.configureUserSettings("U003", 3);     // Detection count
driver.configureUserSettings("U004", 600);   // Power cycle duration

// 4. Enable GPIO channels
driver.configureUserSettings("U005", 0x100000000ULL); // Enable input ch0
driver.configureUserSettings("U006", 0x100000001ULL); // Enable output ch1
```

### 6.2 Data Monitoring Sequence

```cpp
// 1. Request data (non-blocking)
driver.requestData("A001");  // Request SEL event log
driver.requestData("A002");  // Request device status

// 2. Receive responses
uint8_t eventData[12], statusData[12];
driver.receiveSlaveResponse(5, eventData, 1000);
driver.receiveSlaveResponse(5, statusData, 1000);

// 3. Process received data
std::string eventKey = extractKey(eventData);
if (eventKey == "A001") {
    // Process SEL event log data
}
```

## 7. Key Design Benefits

### 7.1 User Simplicity
- **Raw Data Input**: Users provide values directly (250, 1500, 3.14159f)
- **Automatic Conversion**: Driver handles all binary encoding/decoding
- **Cross-Platform**: Same code works on Windows/Linux/ARM
- **Type Safety**: Specific result types for different operations

### 7.2 Protocol Abstraction
- **Hidden Complexity**: Users never see 16-byte frames or protocol details
- **Automatic Routing**: Function codes route to correct API categories
- **Buffer Management**: Automatic buffer checking before transmission
- **Error Recovery**: Intelligent retry mechanism for transient errors

### 7.3 Enterprise Features
- **FTDI Integration**: Built-in USB-RS485 converter support
- **Single Executable**: No separate driver installation required
- **Real-Time Performance**: Fixed-size buffers for deterministic behavior
- **Comprehensive Logging**: Detailed error reporting and diagnostics

## 8. Implementation Notes

- **Address Management**: S001 sets target address for subsequent U/A-series commands
- **Configuration Persistence**: All U-series settings saved to FRAM automatically
- **Buffer Overflow**: Configurable policies (discard oldest/newest, trigger error)
- **Multi-Frame Responses**: Automatic handling for responses >8 bytes
- **Acknowledgment System**: Mandatory acknowledgments for reliability


